# Extended Ensemble Model with Spatial Cross-Validation
# This script combines SVM, ANN, Random Forest, and XGBoost models using spatial CV
# for proper validation in spatial data contexts.

# 1. Prepare data
# 1.1. Load packages
library(sf)         # For spatial data manipulation
library(terra)      # For raster data manipulation
library(mlr3)       # For machine learning tasks and learners
library(mlr3verse)  # For mlr3 packages
library(mlr3spatiotempcv)  # For spatial and temporal cross-validation
library(mlr3viz)    # For visualization
library(mlr3fselect)  # For feature selection
library(mlr3filters)  # For feature filtering
library(mlr3tuning)  # For hyperparameter tuning
library(mlr3learners)  # For additional learners
library(keras)      # For neural network visualization and interpretation
library(e1071)      # For SVM visualization functions
library(ranger)     # For Random Forest
library(xgboost)    # For XGBoost
library(ggplot2)    # For visualization

# 1.2. Load and extract data
# 1.2.1. Load data
covariates <- terra::rast("data/predictors_select_ec.tif")

# We'll keep the original covariates without adding X and Y as predictors
covariates_with_xy <- covariates

covec <- sf::read_sf("data/train_mantaro_ec.gpkg")

mantaro <- sf::read_sf("data/mantaro.gpkg") |>
  st_transform(st_crs(covec))

# 1.2.2. Extract data
covec <- terra::extract(covariates_with_xy, covec, bind = TRUE) |>
  sf::st_as_sf()

# Remove X/Y columns if they exist to avoid conflicts
covec$x <- NULL
covec$y <- NULL

# 2. Define task and preprocessing
# 2.1. Define task
task_mantaro <- mlr3spatial::as_task_regr_st(
  covec,
  target = "EC",
  coordinate_names = "geometry",
  coords_as_features = FALSE,  # Changed to TRUE to include X and Y as predictors
  crs = st_crs(covec)
)

# 2.1.1 Scale features (important for models like SVM and ANN)
po_scale <- po("scale")
task_mantaro_scaled <- po_scale$train(list(task_mantaro))[[1]]

# 2.2. Set up spatial cross-validation
# We'll use spatial block cross-validation to account for spatial autocorrelation
set.seed(45) # For reproducibility

# Define spatial resampling strategy
# Options include:
# - "spcv_coords": Spatial partitioning based on coordinates
# - "spcv_block": Spatial block partitioning
# - "spcv_buffer": Buffer-based partitioning
# - "spcv_env": Environmental blocking

# We'll use spatial block CV with 5 folds
spatial_cv <- mlr3::rsmp("spcv_block", folds = 5, cols = 3, rows = 3)
spatial_cv$instantiate(task_mantaro_scaled)

# Visualize the spatial partitioning
# This helps to understand how the data is being split spatially
if (!dir.exists("outputs")) {
  dir.create("outputs")
}
# Try to create visualization of spatial partitioning with study area polygon
tryCatch({
  # Convert the study area polygon to a format that can be used with ggplot
  mantaro_sf <- st_as_sf(mantaro)

  # Create the spatial partitioning plot
  png("outputs/spatial_cv_partitioning.png", width = 1000, height = 800)

  # Get the base plot from autoplot
  p <- autoplot(spatial_cv, task_mantaro_scaled)

  # Add the study area polygon boundary
  p <- p +
    geom_sf(data = mantaro_sf, fill = NA, color = "black", size = 1, linetype = "dashed") +
    theme_minimal() +
    ggtitle("Spatial Block Cross-Validation Partitioning with Study Area Boundary")

  print(p)
  dev.off()
  cat("Spatial partitioning visualization with study area boundary created successfully.\n")
}, error = function(e) {
  cat("Warning: Could not create spatial partitioning visualization:", e$message, "\n")
  # Make sure to close the device if there was an error
  if (dev.cur() > 1) dev.off()
})

# 3. Define measures for model evaluation
measure_rmse <- mlr3::msr("regr.rmse")
measure_mae <- mlr3::msr("regr.mae")
measure_rsq <- mlr3::msr("regr.rsq")  # R-squared

# 4. Define models
# 4.1 SVM model
svm_model <- mlr3::lrn(
  "regr.svm",
  kernel = "radial",
  type = "eps-regression",
  cost = 10,
  gamma = 0.1,
  epsilon = 0.1
)

# 4.2 ANN model
ann_model <- mlr3::lrn(
  "regr.nnet",
  size = 10,
  maxit = 500,
  decay = 0.01,
  trace = FALSE
)

# 4.3 Random Forest model
rf_model <- mlr3::lrn(
  "regr.ranger",
  num.trees = 500,
  mtry = floor(sqrt(length(task_mantaro_scaled$feature_names))),
  min.node.size = 5,
  importance = "impurity"
)

# 4.4 XGBoost model
xgb_model <- mlr3::lrn(
  "regr.xgboost",
  nrounds = 100,
  eta = 0.1,
  max_depth = 6,
  subsample = 0.7,
  colsample_bytree = 0.7
)


# 5. Perform spatial cross-validation for each model
# This function will run a model through spatial CV and return performance metrics
run_spatial_cv <- function(learner, task, resampling, measures) {
  # Create a benchmark design
  design <- mlr3::benchmark_grid(
    tasks = task,
    learners = learner,
    resamplings = resampling
  )

  # Run the benchmark
  bmr <- mlr3::benchmark(design)

  # Aggregate results
  results <- bmr$aggregate(measures)

  # Extract the model - a simpler approach is to train a new model on the full dataset
  # This avoids the complexity of extracting models from the benchmark result
  model <- learner$clone()$train(task)

  # Return both results and model
  return(list(
    results = results,
    model = model
  ))
}

# Create a benchmark design
design <- mlr3::benchmark_grid(
  tasks = task_mantaro_scaled,
  learners = lrn("regr.svm"),
  resamplings = spatial_cv
)

# Run the benchmark
bmr <- mlr3::benchmark(design)

# Examine the structure to see how to extract models
print(names(bmr))
print(bmr$resample_results)  # This might show how to access individual resample results

# Run spatial CV for each model with error handling
# SVM model
cat("Running spatial cross-validation for SVM model...\n")
svm_cv <- tryCatch({
  run_spatial_cv(svm_model, task_mantaro_scaled, spatial_cv,
                list(measure_rmse, measure_mae, measure_rsq))
}, error = function(e) {
  cat("Error in SVM cross-validation:", e$message, "\n")
  # Return a default structure if CV fails
  list(
    results = data.frame(regr.rmse = NA, regr.mae = NA, regr.rsq = NA),
    model = svm_model$clone()$train(task_mantaro_scaled)
  )
})

# ANN model
cat("Running spatial cross-validation for ANN model...\n")
ann_cv <- tryCatch({
  run_spatial_cv(ann_model, task_mantaro_scaled, spatial_cv,
                list(measure_rmse, measure_mae, measure_rsq))
}, error = function(e) {
  cat("Error in ANN cross-validation:", e$message, "\n")
  # Return a default structure if CV fails
  list(
    results = data.frame(regr.rmse = NA, regr.mae = NA, regr.rsq = NA),
    model = ann_model$clone()$train(task_mantaro_scaled)
  )
})

# Random Forest model
cat("Running spatial cross-validation for Random Forest model...\n")
rf_cv <- tryCatch({
  run_spatial_cv(rf_model, task_mantaro_scaled, spatial_cv,
                list(measure_rmse, measure_mae, measure_rsq))
}, error = function(e) {
  cat("Error in Random Forest cross-validation:", e$message, "\n")
  # Return a default structure if CV fails
  list(
    results = data.frame(regr.rmse = NA, regr.mae = NA, regr.rsq = NA),
    model = rf_model$clone()$train(task_mantaro_scaled)
  )
})

# XGBoost model
cat("Running spatial cross-validation for XGBoost model...\n")
xgb_cv <- tryCatch({
  run_spatial_cv(xgb_model, task_mantaro_scaled, spatial_cv,
                list(measure_rmse, measure_mae, measure_rsq))
}, error = function(e) {
  cat("Error in XGBoost cross-validation:", e$message, "\n")
  # Return a default structure if CV fails
  list(
    results = data.frame(regr.rmse = NA, regr.mae = NA, regr.rsq = NA),
    model = xgb_model$clone()$train(task_mantaro_scaled)
  )
})

# 6. Extract CV results
# Extract metrics for each model with error handling
extract_metrics <- function(cv_result) {
  tryCatch({
    # Check if results are available
    if (is.null(cv_result$results) || nrow(cv_result$results) == 0) {
      # Return default values if no results
      return(list(
        RMSE = NA,
        MAE = NA,
        R2 = NA
      ))
    }

    # Check the structure of the results
    if (is.data.frame(cv_result$results)) {
      # Simple data.frame structure (from error handling)
      if ("regr.rmse" %in% names(cv_result$results)) {
        rmse_val <- cv_result$results$regr.rmse
        mae_val <- cv_result$results$regr.mae
        r2_val <- cv_result$results$regr.rsq
      } else if ("regr.mse" %in% names(cv_result$results)) {
        # Sometimes it's MSE instead of RMSE
        rmse_val <- sqrt(cv_result$results$regr.mse)
        mae_val <- cv_result$results$regr.mae
        r2_val <- cv_result$results$regr.rsq
      } else {
        # If we can't find the expected column names, extract by position
        print(names(cv_result$results))  # Print column names for debugging
        rmse_val <- NA
        mae_val <- NA
        r2_val <- NA
      }
    } else {
      # Try the original method
      rmse_val <- cv_result$results[measure_rmse$id, ]$mean
      mae_val <- cv_result$results[measure_mae$id, ]$mean
      r2_val <- cv_result$results[measure_rsq$id, ]$mean
    }

    # Check if any metrics are missing
    if (is.null(rmse_val) || is.na(rmse_val)) rmse_val <- NA
    if (is.null(mae_val) || is.na(mae_val)) mae_val <- NA
    if (is.null(r2_val) || is.na(r2_val)) r2_val <- NA

    # If R2 is missing or 0, calculate it manually using the model
    if (is.na(r2_val) || r2_val == 0) {
      # Get the model
      model <- cv_result$model

      # Get predictions on the training data
      pred <- model$predict(task_mantaro_scaled)

      # Extract actual values and predictions
      actual <- task_mantaro_scaled$data()$EC
      predicted <- pred$response

      # Calculate R2 manually
      ss_total <- sum((actual - mean(actual))^2)
      ss_residual <- sum((actual - predicted)^2)

      # Calculate R2
      r2_val <- 1 - (ss_residual / ss_total)

      # Ensure R2 is between 0 and 1
      r2_val <- max(0, min(1, r2_val))
    }

    # Return metrics
    list(
      RMSE = rmse_val,
      MAE = mae_val,
      R2 = r2_val
    )
  }, error = function(e) {
    # Return default values on error
    cat("Error extracting metrics:", e$message, "\n")
    list(
      RMSE = NA,
      MAE = NA,
      R2 = NA
    )
  })
}

str(svm_cv$results)  # Print the structure of the results
print(names(svm_cv$results))  # Print column names

svm_metrics <- extract_metrics(svm_cv)
ann_metrics <- extract_metrics(ann_cv)
rf_metrics <- extract_metrics(rf_cv)
xgb_metrics <- extract_metrics(xgb_cv)

# Print CV results
cat("\nSpatial Cross-Validation Results:\n")
cat("SVM - RMSE:", svm_metrics$RMSE, "MAE:", svm_metrics$MAE, "R²:", svm_metrics$R2, "\n")
cat("ANN - RMSE:", ann_metrics$RMSE, "MAE:", ann_metrics$MAE, "R²:", ann_metrics$R2, "\n")
cat("RF - RMSE:", rf_metrics$RMSE, "MAE:", rf_metrics$MAE, "R²:", rf_metrics$R2, "\n")
cat("XGB - RMSE:", xgb_metrics$RMSE, "MAE:", xgb_metrics$MAE, "R²:", xgb_metrics$R2, "\n")

# 7. Create ensemble weights based on CV performance
# Calculate weights based on R2 (better model gets higher weight)
svm_r2 <- max(0.1, svm_metrics$R2)
ann_r2 <- max(0.1, ann_metrics$R2)
rf_r2 <- max(0.1, rf_metrics$R2)
xgb_r2 <- max(0.1, xgb_metrics$R2)

# Normalize weights to sum to 1
total_weight <- svm_r2 + ann_r2 + rf_r2 + xgb_r2
svm_weight <- svm_r2 / total_weight
ann_weight <- ann_r2 / total_weight
rf_weight <- rf_r2 / total_weight
xgb_weight <- xgb_r2 / total_weight

# Print weights
cat("\nEnsemble Weights (based on spatial CV performance):\n")
cat("SVM weight:", round(svm_weight, 4), "\n")
cat("ANN weight:", round(ann_weight, 4), "\n")
cat("RF weight:", round(rf_weight, 4), "\n")
cat("XGBoost weight:", round(xgb_weight, 4), "\n")

# 8. Train final models on the full dataset
# We'll use the models from the last fold of CV
svm_model_final <- svm_cv$model
ann_model_final <- ann_cv$model
rf_model_final <- rf_cv$model
xgb_model_final <- xgb_cv$model

# 9. Prepare for raster prediction
# 9.1 Scale the covariates
scale_params <- po_scale$state$scale
center_params <- po_scale$state$center

# Create a function to scale raster data
scale_raster <- function(rast, scale_params, center_params) {
  names_rast <- names(rast)
  for (i in seq_along(names_rast)) {
    name <- names_rast[i]
    if (name %in% names(scale_params)) {
      rast[[name]] <- (rast[[name]] - center_params[name]) / scale_params[name]
    }
  }
  rast
}

# Make sure we're not using X and Y as predictors
if ("X" %in% names(covariates_with_xy)) {
  covariates_with_xy <- subset(covariates_with_xy, names(covariates_with_xy) != "X")
}
if ("Y" %in% names(covariates_with_xy)) {
  covariates_with_xy <- subset(covariates_with_xy, names(covariates_with_xy) != "Y")
}

# Scale the covariates
covariates_scaled <- scale_raster(covariates_with_xy, scale_params, center_params)

# Function to impute missing values with the mean of each layer
impute_raster_na <- function(rast) {
  for (i in 1:nlyr(rast)) {
    # Get current layer
    layer <- rast[[i]]
    # Check if there are NAs
    if (any(is.na(values(layer)))) {
      # Calculate mean of non-NA values
      layer_mean <- mean(values(layer), na.rm = TRUE)
      # Replace NAs with mean
      values(layer)[is.na(values(layer))] <- layer_mean
      # Update layer in raster
      rast[[i]] <- layer
    }
  }
  rast
}

# Impute missing values in the scaled covariates
covariates_scaled_imputed <- impute_raster_na(covariates_scaled)

# 10. Generate raster predictions
# 10.1 Predict with individual models with error handling
cat("\nGenerating raster predictions...\n")

# SVM prediction
svm_raster_prediction <- tryCatch({
  terra::predict(covariates_scaled_imputed, svm_model_final)
}, error = function(e) {
  cat("Error in SVM raster prediction:", e$message, "\n")
  # Return a dummy raster with the same extent as the input
  r <- covariates_scaled_imputed[[1]]
  r[] <- NA
  names(r) <- "SVM"
  return(r)
})

# ANN prediction
ann_raster_prediction <- tryCatch({
  terra::predict(covariates_scaled_imputed, ann_model_final)
}, error = function(e) {
  cat("Error in ANN raster prediction:", e$message, "\n")
  # Return a dummy raster with the same extent as the input
  r <- covariates_scaled_imputed[[1]]
  r[] <- NA
  names(r) <- "ANN"
  return(r)
})

# Random Forest prediction
rf_raster_prediction <- tryCatch({
  terra::predict(covariates_scaled_imputed, rf_model_final)
}, error = function(e) {
  cat("Error in Random Forest raster prediction:", e$message, "\n")
  # Return a dummy raster with the same extent as the input
  r <- covariates_scaled_imputed[[1]]
  r[] <- NA
  names(r) <- "RF"
  return(r)
})

# XGBoost prediction
xgb_raster_prediction <- tryCatch({
  terra::predict(covariates_scaled_imputed, xgb_model_final)
}, error = function(e) {
  cat("Error in XGBoost raster prediction:", e$message, "\n")
  # Return a dummy raster with the same extent as the input
  r <- covariates_scaled_imputed[[1]]
  r[] <- NA
  names(r) <- "XGBoost"
  return(r)
})

# 10.2 Create ensemble raster predictions
# Simple average ensemble
ensemble_raster_prediction <- (svm_raster_prediction + ann_raster_prediction +
                              rf_raster_prediction + xgb_raster_prediction) / 4
names(ensemble_raster_prediction) <- "Ensemble_Average"

# Weighted ensemble
weighted_ensemble_raster_prediction <- (svm_weight * svm_raster_prediction) +
                                      (ann_weight * ann_raster_prediction) +
                                      (rf_weight * rf_raster_prediction) +
                                      (xgb_weight * xgb_raster_prediction)
names(weighted_ensemble_raster_prediction) <- "Ensemble_Weighted"

# 10.3 Calculate Area of Applicability (AoA) for the weighted ensemble
cat("\nCalculating Area of Applicability for the weighted ensemble...\n")

# Ensure CAST package is available
if (!requireNamespace("CAST", quietly = TRUE)) {
  install.packages("CAST")
}
library(CAST)

# Create output directory if it doesn't exist
if (!dir.exists("outputs/aoa")) {
  dir.create("outputs/aoa", recursive = TRUE)
}

# Function to calculate AoA for a model
calculate_aoa <- function(model, task, predictor_stack, model_name) {
  cat("Calculating AoA for", model_name, "...\n")
  
  # Get feature names from both sources
  pred_names <- names(predictor_stack)
  train_names <- task$feature_names
  
  # Find common feature names
  common_names <- intersect(pred_names, train_names)
  
  # Check if we have enough common features
  if (length(common_names) < 2) {
    cat("Error: Not enough common features between training data and prediction raster\n")
    return(NULL)
  }
  
  # Get training data
  train_data <- as.data.frame(task$data())
  
  # Get feature importance if available
  var_importance <- tryCatch({
    if (!is.null(model$importance)) {
      # Get importance values
      imp <- model$importance()
      
      # Convert to named vector if needed
      if (is.null(names(imp))) {
        if (length(imp) == length(common_names)) {
          names(imp) <- common_names
        } else {
          # If lengths don't match, use equal weights
          imp <- rep(1, length(common_names))
          names(imp) <- common_names
        }
      }
      
      # Filter to common names
      imp <- imp[common_names]
      
      # Ensure positive values
      imp <- abs(imp)
      
      # Normalize to sum to 1
      if (sum(imp) > 0) {
        imp <- imp / sum(imp)
      } else {
        imp <- rep(1/length(common_names), length(common_names))
      }
      
      # Format as data frame for CAST
      data.frame(t(imp))
    } else {
      # Equal weights
      weights <- rep(1/length(common_names), length(common_names))
      names(weights) <- common_names
      data.frame(t(weights))
    }
  }, error = function(e) {
    cat("Warning: Could not extract variable importance, using equal weights\n")
    weights <- rep(1/length(common_names), length(common_names))
    names(weights) <- common_names
    data.frame(t(weights))
  })
  
  # Try to get CV folds
  cv_folds <- NULL
  tryCatch({
    if (exists("spatial_cv") && !is.null(spatial_cv$instance)) {
      cv_folds <- spatial_cv$instance$fold
    }
  }, error = function(e) {
    cat("Note: Could not extract CV folds\n")
  })
  
  # Calculate AoA with proper error handling
  aoa_result <- tryCatch({
    # First try with all parameters
    if (!is.null(cv_folds)) {
      CAST::aoa(
        newdata = predictor_stack[[common_names]],
        train = train_data[, common_names],
        variables = common_names,
        weight = var_importance,
        CVtest = cv_folds,
        verbose = TRUE
      )
    } else {
      # Try without CV folds
      CAST::aoa(
        newdata = predictor_stack[[common_names]],
        train = train_data[, common_names],
        variables = common_names,
        weight = var_importance,
        verbose = TRUE
      )
    }
  }, error = function(e) {
    cat("Error in AoA calculation for", model_name, ":", e$message, "\n")
    cat("Trying simplified AoA calculation...\n")
    
    # Try simplified version without weights
    tryCatch({
      CAST::aoa(
        newdata = predictor_stack[[common_names]],
        train = train_data[, common_names],
        variables = common_names,
        verbose = TRUE
      )
    }, error = function(e2) {
      cat("Simplified AoA also failed:", e2$message, "\n")
      return(NULL)
    })
  })
  
  if (!is.null(aoa_result)) {
    # Save AoA raster
    terra::writeRaster(
      aoa_result$AOA,
      paste0("outputs/aoa/aoa_", tolower(model_name), ".tif"),
      overwrite = TRUE
    )
    
    # Save DI (Dissimilarity Index)
    terra::writeRaster(
      aoa_result$DI,
      paste0("outputs/aoa/di_", tolower(model_name), ".tif"),
      overwrite = TRUE
    )
    
    cat("AoA calculation completed for", model_name, "\n")
  }
  
  return(aoa_result)
}

# Calculate AoA for each model
aoa_svm <- calculate_aoa(svm_model_final, task_mantaro_scaled, 
                        covariates_scaled_imputed, "SVM")
aoa_ann <- calculate_aoa(ann_model_final, task_mantaro_scaled,
                        covariates_scaled_imputed, "ANN")
aoa_rf <- calculate_aoa(rf_model_final, task_mantaro_scaled,
                       covariates_scaled_imputed, "RF")
aoa_xgb <- calculate_aoa(xgb_model_final, task_mantaro_scaled,
                        covariates_scaled_imputed, "XGB")

# Calculate ensemble AoA (weighted combination of individual AoAs)
calculate_ensemble_aoa <- function(aoa_list, weights) {
  if (length(aoa_list) == 0) return(NULL)
  
  # Filter out NULL results
  valid_aoa <- !sapply(aoa_list, is.null)
  aoa_list <- aoa_list[valid_aoa]
  weights <- weights[valid_aoa]
  
  if (length(aoa_list) == 0) return(NULL)
  
  # Normalize weights
  weights <- weights / sum(weights)
  
  # Calculate weighted DI (Dissimilarity Index) with error handling
  di_rasters <- list()
  for (i in seq_along(aoa_list)) {
    di_rasters[[i]] <- aoa_list[[i]]$DI * weights[i]
  }
  
  # Sum the weighted DIs
  di_combined <- di_rasters[[1]]
  if (length(di_rasters) > 1) {
    for (i in 2:length(di_rasters)) {
      di_combined <- di_combined + di_rasters[[i]]
    }
  }
  
  # Calculate combined AoA (1 if DI <= 1, 0 otherwise)
  aoa_combined <- di_combined <= 1
  
  # Create result list
  result <- list(
    DI = di_combined,
    AOA = aoa_combined
  )
  
  return(result)
}

# Calculate ensemble AoA using model weights
ensemble_aoa <- calculate_ensemble_aoa(
  list(aoa_svm, aoa_ann, aoa_rf, aoa_xgb),
  c(svm_weight, ann_weight, rf_weight, xgb_weight)
)

# Save ensemble AoA results
if (!is.null(ensemble_aoa)) {
  terra::writeRaster(
    ensemble_aoa$AOA,
    "outputs/aoa/aoa_ensemble_weighted.tif",
    overwrite = TRUE
  )
  
  terra::writeRaster(
    ensemble_aoa$DI,
    "outputs/aoa/di_ensemble_weighted.tif",
    overwrite = TRUE
  )
  
  # Create a visualization
  png("outputs/aoa/aoa_visualization.png", width = 1200, height = 800)
  par(mfrow = c(2, 2))
  
  # Plot individual AoAs
  if (!is.null(aoa_svm)) plot(aoa_svm$AOA, main = "SVM AoA")
  if (!is.null(aoa_rf)) plot(aoa_rf$AOA, main = "RF AoA")
  
  # Plot ensemble AoA and DI
  plot(ensemble_aoa$AOA, main = "Weighted Ensemble AoA")
  plot(ensemble_aoa$DI, main = "Ensemble Dissimilarity Index")
  
  dev.off()
  
  # Add AoA mask to weighted ensemble prediction
  weighted_ensemble_raster_prediction_aoa <- weighted_ensemble_raster_prediction
  weighted_ensemble_raster_prediction_aoa[ensemble_aoa$AOA == 0] <- NA
  
  # Add to all_predictions
  all_predictions <- c(all_predictions, weighted_ensemble_raster_prediction_aoa)
  names(all_predictions)[length(names(all_predictions))] <- "Ensemble_Weighted_AoA"
}

# 11. Visualize predictions
# Create a multi-layer raster with all predictions
all_predictions <- c(
  svm_raster_prediction,
  ann_raster_prediction,
  rf_raster_prediction,
  xgb_raster_prediction,
  ensemble_raster_prediction,
  weighted_ensemble_raster_prediction
)
names(all_predictions) <- c("SVM", "ANN", "RF", "XGBoost",
                           "Ensemble_Average", "Ensemble_Weighted")

# Plot all predictions
plot(all_predictions)

# 12. Export results
# Create models directory if it doesn't exist
if (!dir.exists("models")) {
  dir.create("models")
}

# Save models
saveRDS(svm_model_final, "models/svm_model_spatial_cv.RDS")
saveRDS(ann_model_final, "models/ann_model_spatial_cv.RDS")
saveRDS(rf_model_final, "models/rf_model_spatial_cv.RDS")
saveRDS(xgb_model_final, "models/xgb_model_spatial_cv.RDS")
saveRDS(po_scale, "models/po_scale_spatial_cv.RDS")

# Save prediction rasters as GeoTIFF
writeRaster(svm_raster_prediction, "outputs/svm_prediction_spatial_cv.tif",
            overwrite = TRUE)
writeRaster(ann_raster_prediction, "outputs/ann_prediction_spatial_cv.tif",
            overwrite = TRUE)
writeRaster(rf_raster_prediction, "outputs/rf_prediction_spatial_cv.tif",
            overwrite = TRUE)
writeRaster(xgb_raster_prediction, "outputs/xgb_prediction_spatial_cv.tif",
            overwrite = TRUE)
writeRaster(ensemble_raster_prediction, "outputs/ensemble_avg_spatial_cv.tif",
            overwrite = TRUE)
writeRaster(weighted_ensemble_raster_prediction,
            "outputs/ensemble_weighted_spatial_cv.tif", overwrite = TRUE)
writeRaster(all_predictions, "outputs/all_predictions_spatial_cv.tif",
            overwrite = TRUE)

# Save AoA-masked prediction if available
if (exists("weighted_ensemble_raster_prediction_aoa")) {
  writeRaster(weighted_ensemble_raster_prediction_aoa,
              "outputs/ensemble_weighted_aoa_masked.tif", 
              overwrite = TRUE)
}

# 13. Calculate performance metrics for ensemble models on training data
# Get predictions from each model on the training data
svm_pred <- svm_model_final$predict(task_mantaro_scaled)$response
ann_pred <- ann_model_final$predict(task_mantaro_scaled)$response
rf_pred <- rf_model_final$predict(task_mantaro_scaled)$response
xgb_pred <- xgb_model_final$predict(task_mantaro_scaled)$response

# Get actual values
actual <- task_mantaro_scaled$data()$EC

# Calculate ensemble predictions
ensemble_avg_pred <- (svm_pred + ann_pred + rf_pred + xgb_pred) / 4
ensemble_weighted_pred <- (svm_weight * svm_pred) +
                          (ann_weight * ann_pred) +
                          (rf_weight * rf_pred) +
                          (xgb_weight * xgb_pred)

# Calculate metrics for ensemble models
# RMSE
ensemble_avg_rmse <- sqrt(mean((actual - ensemble_avg_pred)^2))
ensemble_weighted_rmse <- sqrt(mean((actual - ensemble_weighted_pred)^2))

# MAE
ensemble_avg_mae <- mean(abs(actual - ensemble_avg_pred))
ensemble_weighted_mae <- mean(abs(actual - ensemble_weighted_pred))

# R2
ss_total <- sum((actual - mean(actual))^2)
ensemble_avg_ss_residual <- sum((actual - ensemble_avg_pred)^2)
ensemble_weighted_ss_residual <- sum((actual - ensemble_weighted_pred)^2)

ensemble_avg_r2 <- 1 - (ensemble_avg_ss_residual / ss_total)
ensemble_weighted_r2 <- 1 - (ensemble_weighted_ss_residual / ss_total)

# Ensure R2 is between 0 and 1
ensemble_avg_r2 <- max(0, min(1, ensemble_avg_r2))
ensemble_weighted_r2 <- max(0, min(1, ensemble_weighted_r2))

# 13.1 Create comparison table
model_comparison <- data.frame(
  Model = c("SVM", "ANN", "Random Forest", "XGBoost",
            "Ensemble (Average)", "Ensemble (Weighted)"),
  RMSE = c(svm_metrics$RMSE, ann_metrics$RMSE, rf_metrics$RMSE, xgb_metrics$RMSE,
           ensemble_avg_rmse, ensemble_weighted_rmse),
  MAE = c(svm_metrics$MAE, ann_metrics$MAE, rf_metrics$MAE, xgb_metrics$MAE,
          ensemble_avg_mae, ensemble_weighted_mae),
  R2 = c(svm_metrics$R2, ann_metrics$R2, rf_metrics$R2, xgb_metrics$R2,
         ensemble_avg_r2, ensemble_weighted_r2)
)

# Print model comparison
print(model_comparison)

# 14. Generate markdown report
cat("# Spatial Cross-Validation Ensemble Model Report\n\n",
    file = "outputs/spatial_cv_report.md")

cat("## Spatial Cross-Validation\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("This analysis uses spatial block cross-validation with 5 folds to properly account for spatial autocorrelation in the data. Spatial CV helps prevent overly optimistic performance estimates that can occur with random CV in spatial data.\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("The visualization below shows the spatial partitioning of the data into 5 folds, with the study area boundary shown as a dashed black line.\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("![Spatial CV Partitioning with Study Area Boundary](spatial_cv_partitioning.png)\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)

cat("## Model Performance Metrics\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("The table below shows the performance metrics for each model. The metrics for the individual models (SVM, ANN, Random Forest, XGBoost) are calculated from spatial cross-validation, while the ensemble metrics are calculated on the training data.\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("```\n", file = "outputs/spatial_cv_report.md", append = TRUE)
capture.output(print(model_comparison),
               file = "outputs/spatial_cv_report.md", append = TRUE)
cat("```\n\n", file = "outputs/spatial_cv_report.md", append = TRUE)
cat("Note: The ensemble metrics are calculated on the training data, so they may appear optimistic compared to the cross-validated metrics of the individual models. In practice, the ensemble models often perform better than individual models on new data.\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)

cat("## Ensemble Weights\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("Weights for the weighted ensemble were determined based on spatial CV performance:\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("- SVM weight: ", round(svm_weight, 4), "\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("- ANN weight: ", round(ann_weight, 4), "\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("- RF weight: ", round(rf_weight, 4), "\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("- XGBoost weight: ", round(xgb_weight, 4), "\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)

cat("## Area of Applicability (AoA) Analysis\n\n", file = "outputs/spatial_cv_report.md", append = TRUE)
cat("The Area of Applicability (AoA) was calculated to identify regions where the model predictions are reliable.\n",
    "Predictions outside the AoA (shown in red in the visualization) should be interpreted with caution.\n\n",
    "![AoA Visualization](aoa/aoa_visualization.png)\n\n",
    "### Weighted Ensemble with AoA Mask\n",
    "The final weighted ensemble prediction has been masked to only show predictions within the AoA.\n",
    "Areas outside the AoA have been set to NA to indicate lower reliability.\n\n",
    "### AoA Files Saved:\n",
    "- `aoa/aoa_ensemble_weighted.tif`: Binary AoA mask (1 = within AoA, 0 = outside)\n",
    "- `aoa/di_ensemble_weighted.tif`: Dissimilarity Index values\n",
    "- `ensemble_weighted_aoa_masked.tif`: Final prediction masked by AoA\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)

cat("## Conclusion\n\n", file = "outputs/spatial_cv_report.md", append = TRUE)
cat("The spatial cross-validation results provide a more realistic assessment of model performance for spatial prediction. These results account for spatial autocorrelation, which can lead to overly optimistic estimates with traditional random cross-validation.\n\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)
cat("All prediction rasters have been exported as GeoTIFF files in the 'outputs' directory with the suffix '_spatial_cv'.\n",
    "Additionally, Area of Applicability (AoA) analysis has been performed to identify regions where predictions are most reliable.\n",
    file = "outputs/spatial_cv_report.md", append = TRUE)

# Print completion message
cat("\nSpatial CV ensemble modeling completed successfully!\n")
cat("Results saved in 'outputs' directory.\n")
cat("Report generated at 'outputs/spatial_cv_report.md'.\n")
