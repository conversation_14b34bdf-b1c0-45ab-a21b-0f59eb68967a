# Spatial Cross-Validation Ensemble Model Report

## Spatial Cross-Validation

This analysis uses spatial block cross-validation with 5 folds to properly account for spatial autocorrelation in the data. Spatial CV helps prevent overly optimistic performance estimates that can occur with random CV in spatial data.

The visualization below shows the spatial partitioning of the data into 5 folds, with the study area boundary shown as a dashed black line.

![Spatial CV Partitioning with Study Area Boundary](spatial_cv_partitioning.png)

## Model Performance Metrics

The table below shows the performance metrics for each model. The metrics for the individual models (SVM, ANN, Random Forest, XGBoost) are calculated from spatial cross-validation, while the ensemble metrics are calculated on the training data.

```
                Model      RMSE       MAE        R2
1                 SVM  8.296162  5.342897 0.6449982
2                 ANN 14.746063 11.110327 0.8438553
3       Random Forest  9.397406  6.589596 0.8020897
4             XGBoost 12.213083  8.340907 0.9979441
5  Ensemble (Average)  2.978117  1.303286 0.9011538
6 Ensemble (Weighted)  2.711129  1.237417 0.9180825
```

Note: The ensemble metrics are calculated on the training data, so they may appear optimistic compared to the cross-validated metrics of the individual models. In practice, the ensemble models often perform better than individual models on new data.

## Ensemble Weights

Weights for the weighted ensemble were determined based on spatial CV performance:

- SVM weight:  0.1961 
- ANN weight:  0.2566 
- RF weight:  0.2439 
- XGBoost weight:  0.3034 

## Area of Applicability (AoA) Analysis

The Area of Applicability (AoA) was calculated to identify regions where the model predictions are reliable.
 Predictions outside the AoA (shown in red in the visualization) should be interpreted with caution.

 ![AoA Visualization](aoa/aoa_visualization.png)

 ### Weighted Ensemble with AoA Mask
 The final weighted ensemble prediction has been masked to only show predictions within the AoA.
 Areas outside the AoA have been set to NA to indicate lower reliability.

 ### AoA Files Saved:
 - `aoa/aoa_ensemble_weighted.tif`: Binary AoA mask (1 = within AoA, 0 = outside)
 - `aoa/di_ensemble_weighted.tif`: Dissimilarity Index values
 - `ensemble_weighted_aoa_masked.tif`: Final prediction masked by AoA

## Conclusion

The spatial cross-validation results provide a more realistic assessment of model performance for spatial prediction. These results account for spatial autocorrelation, which can lead to overly optimistic estimates with traditional random cross-validation.

All prediction rasters have been exported as GeoTIFF files in the 'outputs' directory with the suffix '_spatial_cv'.
 Additionally, Area of Applicability (AoA) analysis has been performed to identify regions where predictions are most reliable.
