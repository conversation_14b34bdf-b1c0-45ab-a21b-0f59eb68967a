# 1. Prepare data
# 1.1. Load packages
library(sf)         # For spatial data manipulation
library(terra)      # For raster data manipulation
library(mlr3)       # For machine learning tasks and learners
library(mlr3verse)  # For mlr3 packages
library(mlr3spatiotempcv)  # For spatial and temporal cross-validation
library(mlr3viz)    # For visualization
library(CAST)       # For AOA calculation
library(ggplot2)    # For visualization

# 1.2. Load and extract data
# 1.2.1. Load data
covariates <- terra::rast("data/predictors_select_ec.tif")
covariates_with_xy <- covariates
covec <- sf::read_sf("data/train_mantaro_ec.gpkg")
mantaro <- sf::read_sf("data/mantaro.gpkg") |>
  st_transform(st_crs(covec))

# 1.2.2. Extract data
covec <- terra::extract(covariates_with_xy, covec, bind = TRUE) |>
  sf::st_as_sf()

# Remove X/Y columns if they exist to avoid conflicts
covec$x <- NULL
covec$y <- NULL

predictor_stack <- terra::rast(covariates_with_xy)

# 2. Create task and define models
task <- mlr3spatiotempcv::as_task_regr_st(covec, target = "EC")

# Scale features (important for models like SVM and ANN)
po_scale <- po("scale")
task_scaled <- po_scale$train(list(task))[[1]]

# Define spatial resampling strategy
spatial_cv <- mlr3::rsmp("spcv_block", folds = 5, cols = 3, rows = 3)
spatial_cv$instantiate(task_scaled)

# Define models (same as in mlr3_mantaro_ensemble_spatial_cv_ec.R)
svm_model <- mlr3::lrn(
  "regr.svm",
  kernel = "radial",
  type = "eps-regression",
  cost = 10,
  gamma = 0.1,
  epsilon = 0.1
)

ann_model <- mlr3::lrn(
  "regr.nnet",
  size = 10,
  maxit = 500,
  decay = 0.01,
  trace = FALSE
)

rf_model <- mlr3::lrn(
  "regr.ranger",
  num.trees = 100,
  importance = "impurity",
  mtry = 8,
  min.node.size = 5,
  splitrule = "extratrees"
)

xgb_model <- mlr3::lrn(
  "regr.xgboost",
  nrounds = 100,
  eta = 0.1,
  max_depth = 6,
  subsample = 0.7,
  colsample_bytree = 0.7
)

# 3. Train models
svm_model$train(task_scaled)
ann_model$train(task_scaled)
rf_model$train(task_scaled)
xgb_model$train(task_scaled)

# 4. Get model weights from spatial CV performance
# These weights should match those in mlr3_mantaro_ensemble_spatial_cv_ec.R
# Using the weights from the report
svm_weight <- 0.1918
ann_weight <- 0.2720
rf_weight <- 0.2392
xgb_weight <- 0.2969

# 5. Scale the covariates for prediction
scale_params <- po_scale$state$scale
center_params <- po_scale$state$center

# Create a function to scale raster data
scale_raster <- function(rast, scale_params, center_params) {
  names_rast <- names(rast)
  for (i in seq_along(names_rast)) {
    name <- names_rast[i]
    if (name %in% names(scale_params)) {
      rast[[name]] <- (rast[[name]] - center_params[name]) / scale_params[name]
    }
  }
  rast
}

# Scale the covariates
covariates_scaled <- scale_raster(covariates_with_xy, scale_params, center_params)

# 6. Calculate AOA for each model
# Get CV folds properly
cv_folds <- spatial_cv$instance$fold

# SVM AOA - without model parameter since CAST doesn't support mlr3 SVM models
AOA_svm <- CAST::aoa(
  newdata = covariates_scaled,
  train = as.data.frame(task_scaled$data())[, task_scaled$feature_names],
  variables = task_scaled$feature_names,
  CVtest = cv_folds,
  verbose = FALSE
)

# ANN AOA - without model parameter since CAST doesn't support mlr3 ANN models
AOA_ann <- CAST::aoa(
  newdata = covariates_scaled,
  train = as.data.frame(task_scaled$data())[, task_scaled$feature_names],
  variables = task_scaled$feature_names,
  CVtest = cv_folds,
  verbose = FALSE
)

# RF AOA - with variable importance
AOA_rf <- CAST::aoa(
  newdata = covariates_scaled,
  train = as.data.frame(task_scaled$data())[, task_scaled$feature_names],
  variables = task_scaled$feature_names,
  weight = rf_model$importance(),  # Get importance directly
  CVtest = cv_folds,
  verbose = FALSE
)

# XGBoost AOA - with variable importance
AOA_xgb <- CAST::aoa(
  newdata = covariates_scaled,
  train = as.data.frame(task_scaled$data())[, task_scaled$feature_names],
  variables = task_scaled$feature_names,
  weight = xgb_model$importance(),  # Get importance directly
  CVtest = cv_folds,
  verbose = FALSE
)

# 7. Create weighted ensemble AOA
# Combine the DI values from each model using the ensemble weights
DI_weighted <- (svm_weight * AOA_svm$DI +
                ann_weight * AOA_ann$DI +
                rf_weight * AOA_rf$DI +
                xgb_weight * AOA_xgb$DI)

# Create a new AOA object for the weighted ensemble
AOA_ensemble <- AOA_rf  # Use one of the AOA objects as a template
AOA_ensemble$DI <- DI_weighted
AOA_ensemble$AOA <- DI_weighted <= AOA_rf$parameters$threshold  # Use same threshold logic

# 8. Plot and save AOA results
# Create output directory if it doesn't exist
if (!dir.exists("outputs/aoa")) {
  dir.create("outputs/aoa", recursive = TRUE)
}

# Plot individual AOAs
png("outputs/aoa/svm_aoa.png", width = 1000, height = 800)
plot(AOA_svm$AOA, main = "SVM Area of Applicability")
plot(st_geometry(mantaro), add = TRUE, border = "black", lwd = 1.5)
dev.off()

png("outputs/aoa/ann_aoa.png", width = 1000, height = 800)
plot(AOA_ann$AOA, main = "ANN Area of Applicability")
plot(st_geometry(mantaro), add = TRUE, border = "black", lwd = 1.5)
dev.off()

png("outputs/aoa/rf_aoa.png", width = 1000, height = 800)
plot(AOA_rf$AOA, main = "Random Forest Area of Applicability")
plot(st_geometry(mantaro), add = TRUE, border = "black", lwd = 1.5)
dev.off()

png("outputs/aoa/xgb_aoa.png", width = 1000, height = 800)
plot(AOA_xgb$AOA, main = "XGBoost Area of Applicability")
plot(st_geometry(mantaro), add = TRUE, border = "black", lwd = 1.5)
dev.off()

png("outputs/aoa/ensemble_aoa.png", width = 1000, height = 800)
plot(AOA_ensemble$AOA, main = "Weighted Ensemble Area of Applicability")
plot(st_geometry(mantaro), add = TRUE, border = "black", lwd = 1.5)
dev.off()

# Save AOA rasters
writeRaster(AOA_svm$AOA, "outputs/aoa/svm_aoa.tif", overwrite = TRUE)
writeRaster(AOA_ann$AOA, "outputs/aoa/ann_aoa.tif", overwrite = TRUE)
writeRaster(AOA_rf$AOA, "outputs/aoa/rf_aoa.tif", overwrite = TRUE)
writeRaster(AOA_xgb$AOA, "outputs/aoa/xgb_aoa.tif", overwrite = TRUE)
writeRaster(AOA_ensemble$AOA, "outputs/aoa/ensemble_aoa.tif", overwrite = TRUE)

# Save DI rasters (dissimilarity index)
writeRaster(AOA_svm$DI, "outputs/aoa/svm_di.tif", overwrite = TRUE)
writeRaster(AOA_ann$DI, "outputs/aoa/ann_di.tif", overwrite = TRUE)
writeRaster(AOA_rf$DI, "outputs/aoa/rf_di.tif", overwrite = TRUE)
writeRaster(AOA_xgb$DI, "outputs/aoa/xgb_di.tif", overwrite = TRUE)
writeRaster(AOA_ensemble$DI, "outputs/aoa/ensemble_di.tif", overwrite = TRUE)

# Print completion message
cat("AOA analysis completed for all models and weighted ensemble.\n")
plot(AOA_mlr3$AOA)
